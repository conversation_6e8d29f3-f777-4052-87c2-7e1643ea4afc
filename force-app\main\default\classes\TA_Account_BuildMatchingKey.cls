/********************************************************************************************

   @ Func Area	:  Account Management

   @ Author	:  <PERSON>

   @ Date	:  12 August 2024

   @ Description	:   This class is responsible for processing new accounts and building the Sales Territory Matching Key (SalesTerritoryMatchingKey__c)
                        This key will be compared against Keys in the Sales Territories Object to identify applicable matches.


   @ Developer Notes   :

   @ Test Class	:   AccountTestPack

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                      https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 30 October 2024
   @ Last Modified Reason  : Creation



********************************************************************************************/
public without sharing class TA_Account_BuildMatchingKey implements TriggerAction.AfterInsert, TriggerAction.AfterUpdate {
  //Dependent Class variables
  @TestVisible
  private static DmlHelper dmlHelper = new DmlHelper();
  @TestVisible
  private static SalesTerritoriesSelector salesTerritoriesSelector = new SalesTerritoriesSelector();
  @TestVisible
  private static AccountService accountService = new AccountService(); //Mock AccountService Class
  //Class variables
  @TestVisible
  private static Boolean BYPASS_VALIDATION_RULE = false;

  List<Account> accountsToProcess = new List<Account>();
  List<Account> accsToUpdate = new List<Account>();

  public void afterInsert(List<Account> newList) {
  }

  public void afterUpdate(List<Account> newList, List<Account> oldList) {
    Map<Id, Account> oldMap = new Map<Id, Account>(oldList);

    for (Account newAccount : newList) {
      Account oldAccount = oldMap.get(newAccount.Id);

      // Check if any monitored fields changed
      if (
        TA_Account_BuildMatchingKey.haveFieldsChanged(oldAccount, newAccount)
      ) {
        System.debug(
          'Account monitored fields have changed -> Account Name -> ' +
          newAccount.Name
        );
        accountsToProcess.add(newAccount);
      }
    }

    if (!accountsToProcess.isEmpty()) {
      buildSalesTerritoryMatchingKey(newList);
    }
  }

  /*********************************************************************************** */

  public void buildSalesTerritoryMatchingKey(List<Account> accounts) {
    for (Account account : accounts) {
      String salesChannel = account.SalesChannel__c;
      String salesTeam = account.SalesTeam__c;
      String shippingCountry = account.ShippingCountry;
      String shippingState = account.ShippingState;
      String shippingPostalCode = account.ShippingPostalCode;

      String matchingKeyVariant = '';
      String salesTerritoryMatchingKey = '';

      if (
        (shippingCountry == 'United States' || shippingCountry == 'Canada') &&
        shippingState != 'California'
      ) {
        matchingKeyVariant = 'variant 1';
      }

      if (
        shippingCountry == 'United States' &&
        shippingState == 'California' &&
        String.isBlank(shippingPostalCode)
      ) {
        matchingKeyVariant = 'variant 2';
      }

      if (
        shippingCountry == 'United States' &&
        shippingState == 'California' &&
        !String.isBlank(shippingPostalCode)
      ) {
        matchingKeyVariant = 'variant 3';
      }

      if (shippingCountry != 'United States' && shippingCountry != 'Canada') {
        matchingKeyVariant = 'variant 4';
      }

      switch on matchingKeyVariant {
        when 'variant 1' {
          //System.debug('matchingKeyVariant -> ' + matchingKeyVariant);
          salesTerritoryMatchingKey =
            removeSpecialCharacters(salesChannel) +
            removeSpecialCharacters(salesTeam) +
            removeSpecialCharacters(shippingCountry) +
            removeSpecialCharacters(shippingState);
        }
        when 'variant 2' {
          //System.debug('matchingKeyVariant -> ' + matchingKeyVariant);
          salesTerritoryMatchingKey =
            removeSpecialCharacters(salesChannel) +
            removeSpecialCharacters(salesTeam) +
            removeSpecialCharacters(shippingCountry) +
            removeSpecialCharacters(shippingState);
        }
        when 'variant 3' {
          //System.debug('matchingKeyVariant -> ' + matchingKeyVariant);
          salesTerritoryMatchingKey =
            removeSpecialCharacters(salesChannel) +
            removeSpecialCharacters(salesTeam) +
            removeSpecialCharacters(shippingCountry) +
            removeSpecialCharacters(shippingState) +
            removeSpecialCharacters(shippingPostalCode);
        }
        when 'variant 4' {
          //System.debug('matchingKeyVariant -> ' + matchingKeyVariant);
          salesTerritoryMatchingKey =
            removeSpecialCharacters(salesChannel) +
            removeSpecialCharacters(salesTeam) +
            removeSpecialCharacters(shippingCountry);
        }
        when else {
        }
      }

      accsToUpdate.add(
        new Account(
          Id = account.Id,
          SalesTerritoryMatchingKey__c = salesTerritoryMatchingKey
        )
      );
    }

    if (accsToUpdate.size() > 0) {
      TriggerBase.bypass('Account');
      accountService.updateAccounts(
        accsToUpdate,
        'TA_Account_BuildMatchingKey'
      );
      TriggerBase.clearBypass('Account');
    }
  }

  private String removeSpecialCharacters(String inputText) {
    String result = '';
    if (!String.isBlank(inputText)) {
      // Replace all special characters with a single space
      result = inputText.replace(',', '');
      result = result.deleteWhitespace();
      result = result.replace('\'', '');
      result = result.replace('+', '');
    }
    // Return the modified string
    return result;
  }

  // Method to check if any of the specified fields have changed
  public static Boolean haveFieldsChanged(
    Account oldAccount,
    Account newAccount
  ) {
    // List of fields to check
    Set<String> fieldsToCheck = new Set<String>{
      'SalesChannel__c',
      'SalesTeam__c',
      'ShippingCountry',
      'ShippingState',
      'ShippingPostalCode'
    };

    // Check each field for changes
    for (String field : fieldsToCheck) {
      if (oldAccount.get(field) != newAccount.get(field)) {
        return true;
      }
    }
    return false;
  }
}