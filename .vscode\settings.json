{"search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/.sfdx": true}, "workbench.colorCustomizations": {"activityBar.activeBackground": "#65c89b", "activityBar.background": "#65c89b", "activityBar.foreground": "#15202b", "activityBar.inactiveForeground": "#15202b99", "activityBarBadge.background": "#945bc4", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#15202b99", "sash.hoverBorder": "#65c89b", "statusBar.background": "#42b883", "statusBar.foreground": "#15202b", "statusBarItem.hoverBackground": "#359268", "statusBarItem.remoteBackground": "#42b883", "statusBarItem.remoteForeground": "#15202b", "titleBar.activeBackground": "#42b883", "titleBar.activeForeground": "#15202b", "titleBar.inactiveBackground": "#42b88399", "titleBar.inactiveForeground": "#15202b99"}, "peacock.color": "#42b883", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "prettier.requireConfig": true, "prettier.documentSelectors": ["**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}"], "[apex]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[xml]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}