/* Removed inline display styles - component now shows modal directly */

/* Removed unused inline display CSS */

.user-grid {
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(400px, 1fr)
  ); /* Increased from 300px */
  gap: 0.75rem;
  max-height: 400px;
  overflow-y: auto;
}

.user-card {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #d8dde6;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.user-card:hover {
  border-color: #1589ee;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-card.selected {
  border-color: #1589ee;
  background-color: #f3f9ff;
}

.user-card.selected .selection-indicator {
  opacity: 1;
  color: #1589ee;
}

.user-avatar {
  margin-right: 0.75rem;
}

.user-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #181818;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.875rem;
  color: #706e6b;
}

.selection-indicator {
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: 0.5rem;
}

.follower-card.selected .selection-indicator {
  color: #c23934;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #706e6b;
}

.empty-state h3 {
  margin: 1rem 0 0.5rem 0;
  font-size: 1.125rem;
  color: #181818;
}

.empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

.search-input {
  margin-bottom: 1rem;
}

.action-button {
  min-width: 150px;
}

.slds-spinner_container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9999;
}

/* Floating Popup Container - positioned absolutely */
.floating-popup-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  max-height: 90vh;
  overflow: hidden;
  animation: popupFadeIn 0.2s ease-out;
}

/* Ensure complete cleanup when modal is hidden */
:host([data-modal-hidden="true"]) .floating-popup-container,
:host([data-modal-hidden="true"]) .close-button,
:host([data-modal-hidden="true"]) * {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Specific styling for our case follow popup */
.case-follow-popup {
  /* Ensure this popup has the highest z-index to avoid conflicts */
  z-index: 10000 !important;
}

.case-follow-popup .close-button {
  /* Ensure close button is properly contained within our popup */
  position: relative;
  z-index: 1;
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Floating Popup Card */
.floating-popup-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e5e5;
  max-width: 80rem;
  width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Popup Header */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.header-content {
  flex: 1;
}

.popup-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #181818;
  margin: 0 0 0.25rem 0;
}

.popup-subtitle {
  font-size: 0.875rem;
  color: #706e6b;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 1rem;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #f3f2f2;
}

/* Popup Body */
.popup-body {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

/* Popup Footer */
.popup-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e5e5;
  background: #f8f9fa;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .floating-popup-card {
    width: 95vw;
    max-width: none;
    margin: 1rem;
  }

  .popup-header {
    padding: 1rem;
  }

  .popup-body {
    padding: 1rem;
  }

  .popup-footer {
    padding: 1rem;
    flex-direction: column;
  }
}