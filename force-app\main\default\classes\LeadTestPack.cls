/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  08 July 2024

   @ Description	:   A test class containing test methods for apex automation on the Lead Object

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   TA_Lead_EmailNotifications

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 08 July 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public class LeadTestPack {
    //Test LeadTrigger
    @isTest
    static void test_LeadTrigger() {
        Test.startTest();
        List<lead> newList = new List<Lead>();

        newList.add(
            new Lead(
                FirstName = 'ACME',
                LastName = 'Enterprises',
                Company = 'ACME'
            )
        );

        insert newList;
        Test.stopTest();
    }

    //Test LeadSelector class
    @isTest
    static void test_LeadSelector() {
        LeadSelector leadSelector = new LeadSelector();
        Integer queryLimit = 100;
        Set<Id> leadIds = new Set<Id> {MockerUtils.generateId(Lead.SObjectType), MockerUtils.generateId(Lead.SObjectType)};
        Test.startTest();
        leadSelector.setQueryLimit(queryLimit);
        leadSelector.selectLeadsByIds(leadIds);
        Test.stopTest();
    }

    //Test LeadService class
    @isTest
    static void test_LeadService() {
        Test.startTest();
        // Given
        Id validLeadId = MockerUtils.generateId(Lead.SObjectType);
        List<Id> leadIds = new List<Id> {MockerUtils.generateId(Lead.SObjectType), MockerUtils.generateId(Lead.SObjectType)};

        Lead expectedLead = (Lead)MockerUtils.updateObjectState(
            new Lead(
                Id = validLeadId,
                FirstName = 'Joe',
                LastName = 'Bloggs',
                Company = 'ACME'
            ),
            new Map<String, Object> {
            'Name' => 'Joe Bloggs'
        }
        );

        List<Lead> leadsToInsert = new List<Lead> {expectedLead};
        List<Lead> leadsToUpdate = new List<Lead> {expectedLead};

        Mocker mocker = Mocker.startStubbing();

        DmlHelper dmlHelperMock = (DmlHelper)mocker.mock(DmlHelper.class);

        dmlHelperMock.insertObjects(leadsToInsert, 'LeadService');
        Mocker.MethodRecorder insertObjectsRec = mocker.when()
            .withAnyValues()
            .getMethodRecorder();

        dmlHelperMock.updateObjects(leadsToUpdate, 'LeadServiceUpdate');
        Mocker.MethodRecorder updateObjectsRec = mocker.when()
            .withAnyValues()
            .getMethodRecorder();

        LeadSelector leadSelectorMock = (LeadSelector)mocker.mock(LeadSelector.class);

        mocker.when(leadSelectorMock.selectLeadsByIds(new Set<Id> { validLeadId }))
        .thenReturn(new List<Lead> { expectedLead });

        mocker.when(leadSelectorMock.selectLeadsByIds(null))
        .withAnyValues()
        .thenReturn(new List<Lead>());

        // Going to the execution phase
        mocker.stopStubbing();

        // Replacing the real instance by the mocked one
        LeadService.leadSelector = leadSelectorMock;
        LeadService.dmlHelper = dmlHelperMock;

        // When 1
        //test method public String getLeadName(Id leadId) on LeadService
        String leadName = new LeadService().getLeadName(validLeadId);

        // Then 1
        System.assertEquals('Joe Bloggs', leadName);

        // When 2
        //test method public void createLeads(List<Lead> leadsToInsert, String Source) on LeadService
        new LeadService().createLeads(leadsToInsert, 'LeadTestPack.test_LeadService()');

        // Then 2
        System.assertEquals(1, insertObjectsRec.getCallsCount());

        // When 3
        //test method public void updateLeads(List<Lead> leadsToUpdate, String Source) on LeadService
        new LeadService().updateLeads(leadsToUpdate, 'LeadTestPack.test_LeadService()');

        // Then 3
        System.assertEquals(1, updateObjectsRec.getCallsCount());

        Test.stopTest();
    }

    //Test TA_Lead_EmailNotifications class
    @isTest
    static void test_TA_Lead_EmailNotifications() {
        // Setup Test Data
        List<Lead> newLeads = new List<Lead>();
        List<Lead> oldLeads = new List<Lead>();
        Id fakeLeadId = MockerUtils.generateId(Lead.SObjectType);

        //Prep a list of old records to be passed to the Trigger Action for processing
        /*oldLeads.add(
            new lead(
                Id = fakeLeadId,
                FirstName = 'Joe',
                LastName = 'Blogg',
                Status = 'Working'
            )
           );

           //Prep a list of new records to be passed to the Trigger Action for processing
           newLeads.add(
            new lead(
                Id = fakeLeadId,
                FirstName = 'Joe',
                LastName = 'Blogg',
                Status = 'Nurturing'
            )
           );
         */

        lead initialLead = new lead(
            FirstName = 'Joe',
            LastName = 'Blogg',
            Company = 'ACME',
            Status = 'Working'
        );

        insert initialLead;

        //Prep a list of old records to be passed to the Trigger Action for processing
        oldLeads.add(
            new lead(
                Id = initialLead.Id,
                FirstName = 'Joe',
                LastName = 'Blogg',
                Status = 'Working'
            )
        );

        //Prep a list of new records to be passed to the Trigger Action for processing
        newLeads.add(
            new lead(
                Id = initialLead.Id,
                FirstName = 'Joe',
                LastName = 'Blogg',
                Status = 'Nurturing'
            )
        );

        User myTestUser = TestFactory.createAdminUser();
        Group groupToInsert = Utilities.createPublicGroup('Lead_Status_Nurture_Notifications');

        Test.startTest();
        insert myTestUser;
        insert groupToInsert;
        new TA_Lead_EmailNotifications().afterUpdate(newLeads, oldLeads);
        Test.stopTest();
    }
}