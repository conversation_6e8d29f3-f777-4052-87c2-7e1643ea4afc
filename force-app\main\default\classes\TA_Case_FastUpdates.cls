/********************************************************************************************

   @ Func Area	:  Case Management

   @ Author	:  <PERSON>

   @ Date	:  31 May 2024

   @ Description	:   This class is responsible for processing new cases before they are committed to the Database.  The logic
                        will check if newly inserted cases meet defined trigger criteria and then perform updates to the case in
                        the before insert/update context

   @ Developer Notes   :

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                        https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 31 May 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public without sharing class TA_Case_FastUpdates implements TriggerAction.BeforeInsert, TriggerAction.BeforeUpdate {
  //Class variables
  @TestVisible
  private static AccountSelector accountSelector = new AccountSelector(); //Mock Selector Class
  @TestVisible
  private static UserSelector userSelector = new UserSelector(); //Mock Selector Class
  @TestVisible
  private static DmlHelper dmlHelper = new DmlHelper(); //DML Helper class

  @TestVisible
  private static final String CASE_INSERT_STATUS_INVALID_ERROR = 'Valid Status for a newly created case are New';
  @TestVisible
  private static Boolean BYPASS_VALIDATION_RULE = false;

  //Before Insert Variables
  //Before Update Variables
  List<Case> casesToProcessCriteria1 = new List<Case>();

  public void beforeInsert(List<Case> newList) {
    //Fetch Map of User Id to User record
    Map<Id, User> userIdToUserMap = TA_Case_Queries.getInstance()
      .userIdToUserMap;

    //Fetch Map of User Id to Boolean (is Bypass Validation Rule Permission Set assigned?)
    Map<Id, Boolean> userIdToBypassValidationBooleanMap = TA_Case_Queries.getInstance()
      .userIdToBypassValidationBooleanMap;

    //Fetch User Info
    String userEmail = userIdToUserMap.get(UserInfo.getUserId())?.Email;
    //Can user bypass validation rules?
    BYPASS_VALIDATION_RULE = userIdToBypassValidationBooleanMap.get(
      UserInfo.getUserId()
    );

    for (Case newCase : newList) {
      //Trigger Criteria 1
      if (newCase.Type == 'Complaint') {
        newCase.Level__c = 'Level 3';
      }
    }
  }

  public void beforeUpdate(List<Case> newList, List<Case> oldList) {
    //system.debug('TA_Case_FastUpdates.beforeUpdate -> trigger context');

    //Fetch Map of User Id to User record
    Map<Id, User> userIdToUserMap = TA_Case_Queries.getInstance()
      .userIdToUserMap;

    //Fetch Map of User Id to Boolean (is Bypass Validation Rule Permission Set assigned?)
    Map<Id, Boolean> userIdToBypassValidationBooleanMap = TA_Case_Queries.getInstance()
      .userIdToBypassValidationBooleanMap;

    //Fetch User Info
    String userEmail = userIdToUserMap.get(UserInfo.getUserId())?.Email;

    //Can user bypass validation rules?
    BYPASS_VALIDATION_RULE = userIdToBypassValidationBooleanMap.get(
      UserInfo.getUserId()
    );

    Map<Id, Case> oldMap = new Map<Id, Case>(oldList);

    for (Case newCase : newList) {
      Case oldCase = oldMap.get(newCase.Id);

      //Trigger Criteria 1
      if (oldCase.Type != newCase.Type && newCase.Type == 'Complaint') {
        newCase.Level__c = 'Level 3';
      }
      //Trigger Criteria 2
      if (
        oldCase.OwnerId != newCase.OwnerId &&
        String.valueOf(oldCase.OwnerId).startsWith('00G')
      ) {
        Group supportQueueGroup = Utilities.getPublicGroup(
          'Scientific Support'
        );
        Group oemSupportQueueGroup = Utilities.getPublicGroup('OEM Support');
        if (
          supportQueueGroup != null &&
          oldCase.OwnerId == supportQueueGroup.Id
        ) {
          newCase.Status = 'Open';
        }
        if (
          oemSupportQueueGroup != null &&
          oldCase.OwnerId == oemSupportQueueGroup.Id
        ) {
          newCase.Status = 'Open';
        }
      }
      //Trigger Criteria 3
      if (
        newCase.Type == 'Complaint' &&
        oldCase.Verification_Status__c == null &&
        newCase.Verification_Status__c == 'Verified'
      ) {
        newCase.Verification_Date__c = System.now();
      }
      //Trigger Criteria 4
      if (
        CollectionUtils.isNullOrBlank(oldCase.MasterRecordId) &&
        !CollectionUtils.isNullOrBlank(newCase.MasterRecordId)
      ) {
        Id mergedCasesQueueId = [
          SELECT Id
          FROM Group
          WHERE Type = 'Queue' AND DeveloperName = 'Merged_Cases'
          LIMIT 1
        ]
        .Id;
        //newCase.OwnerId = mergedCasesQueueId;
        //newCase.MergedCase__c = true;
      }
    }
  }
}