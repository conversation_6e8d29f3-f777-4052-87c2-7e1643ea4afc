/********************************************************************************************

   @ Func Area	:  Account Territory Management

   @ Author	:  <PERSON>

   @ Date	:  01 November 2024

   @ Description	:   This class is responsible for processing new accounts and associating the matched Sales Territory.

   @ Developer Notes   :

   @ Test Class	:   AccountTestPack

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                    https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 01 November 2024
   @ Last Modified Reason  : Creation

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 03 July 2025
   @ Last Modified Reason  : Refactoring

********************************************************************************************/
public without sharing class TA_Account_TerritoryAssociations implements TriggerAction.AfterInsert, TriggerAction.AfterUpdate {
  public TA_Account_TerritoryAssociations() {
    initializeTerritory2Map();
    initializeTerritoryNameStringToTerritory2IdMap();
  }

  //Dependent Class variables
  @TestVisible
  private static Territory2Selector territory2Selector = new Territory2Selector(); //Mock Territory2Selector Class

  @TestVisible
  private static ObjectTerritory2AssociationSelector objectTerritory2AssociationSelector = new ObjectTerritory2AssociationSelector(); //Mock ObjectTerritory2AssociationSelector Class

  // Map to hold Territory Name to Territory2.Id data
  private static Map<String, Id> territoryNameStringToTerritory2IdMap = new Map<String, Id>();

  //List of all Territory2 records
  private static List<Territory2> allTerritory2 = new List<Territory2>();

  //List of ObjectTerritory2Association to insert
  private List<ObjectTerritory2Association> newTerritoryAssociations = new List<ObjectTerritory2Association>();
  private List<ObjectTerritory2Association> deleteTerritoryAssociations = new List<ObjectTerritory2Association>();

  // Method to initialize the userIdToTerritoryIdMap
  private void initializeTerritory2Map() {
    allTerritory2 = territory2Selector.selectTerritory2ByModelName('WMG Territory Model');
  }

  // Method to initialize the territoryNameStringToTerritory2IdMap
  private void initializeTerritoryNameStringToTerritory2IdMap() {
    for (Territory2 singleTerritory2 : allTerritory2) {
      territoryNameStringToTerritory2IdMap.put(singleTerritory2.Name, singleTerritory2.Id);
    }
  }

  public void afterInsert(List<Account> newList) {
  }

  public void afterUpdate(List<Account> newList, List<Account> oldList) {
    Map<Id, Account> oldMap = new Map<Id, Account>(oldList);
    for (Account newAccount : newList) {
      Account oldAccount = oldMap.get(newAccount.Id);
    }
  }

  public void processAccountsForTerritoryAssignment(List<Account> accounts) {
    Set<Id> accountIds = new Set<Id>();
    for (Account account : accounts) {
      accountIds.add(account.Id);
    }

    Map<Id, List<ObjectTerritory2Association>> existingAssociations = objectTerritory2AssociationSelector.selectObjectTerritory2AssociationByAccountIds(
      accountIds
    );

    // Create a Set to track Account-Territory combinations to avoid duplicates
    Set<String> accountTerritoryPairs = new Set<String>();

    for (Account account : accounts) {
      String territoryId = territoryNameStringToTerritory2IdMap.get(
        account.RecommendedSalesTerritory__c
      );
      //Territory exists so proceed to remove existing territories first before assigning the new territory
      if (!String.isBlank(territoryId)) {
        //Get the existing territories for the account from the map
        List<ObjectTerritory2Association> existing = existingAssociations.get(account.Id);
        //Check for existing territories
        if (CollectionUtils.isNotEmpty(existing)) {
          //There are existing territories so delete them first
          deleteTerritoryAssociations.addAll(existing);
        }
        //Proceed to assign the new territory if not already in our tracking set
        String accountTerritoryKey = account.Id + '-' + territoryId;
        if (!accountTerritoryPairs.contains(accountTerritoryKey)) {
          assignActualTerritoryToAccount(account.Id, Id.valueOf(territoryId));
          accountTerritoryPairs.add(accountTerritoryKey);
        }
      }
    }

    //Check for existing territories to delete
    if (CollectionUtils.isNotEmpty(deleteTerritoryAssociations)) {
      if (!Test.isRunningTest()) {
        delete deleteTerritoryAssociations;
      }
    }

    //Check for new territories to insert
    if (CollectionUtils.isNotEmpty(newTerritoryAssociations)) {
      if (!Test.isRunningTest()) {
        Database.SaveResult[] results = Database.insert(newTerritoryAssociations, false);

        // Log any errors for debugging
        for (Integer i = 0; i < results.size(); i++) {
          if (!results[i].isSuccess()) {
            // Log the specific record that failed
            for (Database.Error err : results[i].getErrors()) {
              System.debug('Failed Account ID: ' + newTerritoryAssociations[i].ObjectId);
              System.debug('Failed Territory ID: ' + newTerritoryAssociations[i].Territory2Id);
              System.debug('Error status code: ' + err.getStatusCode());
              System.debug('Error message: ' + err.getMessage());
            }
          }
        }
      }
    }
  }

  // Method to assign an actual Territory2 to an Account
  public void assignActualTerritoryToAccount(Id accountId, Id territoryId) {
    // Create a new TerritoryAssignment record
    ObjectTerritory2Association territoryAssignment = new ObjectTerritory2Association(
      ObjectId = accountId,
      Territory2Id = territoryId,
      AssociationCause = 'Territory2Manual'
    );
    //Add to collection for insertion later on
    newTerritoryAssociations.add(territoryAssignment);
  }

  public void deletePreviousTerritoryAssociations(
    List<ObjectTerritory2Association> existingAssociations
  ) {
    delete existingAssociations;
  }
}
