/********************************************************************************************

   @ Func Area	:  Lead Notification Management

   @ Author	:  <PERSON>

   @ Date	:  08 July 2024

   @ Description	:   This class is responsible for processing changes made to lead records.
                        When appicable trigger criteria are met then an email notification will be sent to designated parties.

   @ Developer Notes   :

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                      https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 08 July 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public without sharing class TA_Lead_EmailNotifications implements TriggerAction.AfterInsert, TriggerAction.AfterUpdate {
  //Class Variables
  @TestVisible
  private static LeadSelector leadSelector = new LeadSelector(); //Mock Selector Class
  @TestVisible
  private static DmlHelper dmlHelper = new DmlHelper(); //Mock DmlHelper Class
  @TestVisible
  private static LeadService leadService = new LeadService(); //Mock Service Class

  List<Lead> leadsToProcess = new List<Lead>();
  public List<String> emailRecipients = new List<String>();
  public String nuturePublicGroupDeveloperName = 'Lead_Status_Nurture_Notifications';
  public String nutureEmailTemplateName = 'Lead Status = Nurture';

  public void afterInsert(List<Lead> newList) {
  }

  public void afterUpdate(List<Lead> newList, List<Lead> oldList) {
    Map<Id, Lead> oldMap = new Map<Id, Lead>(oldList);

    for (Lead newLead : newList) {
      Lead oldLead = oldMap.get(newLead.Id);
      if (oldLead.Status != 'Nurturing' && newLead.Status == 'Nurturing') {
        leadsToProcess.add(newLead);
      }
    }

    if (leadsToProcess.size() > 0) {
      List<User> recipientsUsersList = Utilities.getGroupMemberUsers(nuturePublicGroupDeveloperName);

      if (recipientsUsersList.size() > 0) {
        EmailManager em = new EmailManager();
        em.SendEmailWithTemplate(nutureEmailTemplateName, null, recipientsUsersList, null, leadsToProcess, null);
      }
    }
  }
}