import { LightningElement, api, wire, track } from "lwc";
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import { CloseActionScreenEvent } from "lightning/actions";
import getAvailableUsers from "@salesforce/apex/CaseFollowLWCController.getAvailableUsers";
import getCurrentFollowers from "@salesforce/apex/CaseFollowLWCController.getCurrentFollowers";
import followCase from "@salesforce/apex/CaseFollowLWCController.followCase";
import unfollowCase from "@salesforce/apex/CaseFollowLWCController.unfollowCase";

export default class CaseFollowLWC extends LightningElement {
  _recordId;

  @api
  get recordId() {
    return this._recordId;
  }

  set recordId(value) {
    console.log("recordId setter called with value:", value);
    this._recordId = value;

    // If we have a recordId and haven't loaded followers yet, load them
    if (value && !this.hasRendered) {
      console.log("Loading followers from recordId setter");
      this.loadCurrentFollowers();
    }
  }
  @track showModal = true; // Show modal immediately when component loads
  @track availableUsers = [];
  @track currentFollowers = [];
  @track selectedUsers = [];
  @track searchTerm = "";
  @track isLoading = false;
  @track activeTab = "follow"; // Default to "Add Followers" tab
  @track isRecordAction = false;

  wiredUsersResult;
  hasRendered = false;

  connectedCallback() {
    console.log("connectedCallback called");
    console.log("recordId at connectedCallback:", this.recordId);

    // Detect if we're running in a Quick Action context
    this.detectQuickActionContext();

    // Clean up any existing modal artifacts when component loads
    this.cleanupModalArtifacts();
  }

  detectQuickActionContext() {
    // Check if we're inside a Quick Action modal by looking for the wrapper elements
    setTimeout(() => {
      const quickActionWrapper = document.querySelector(
        ".runtime_platform_actionsQuickActionWrapper"
      );
      const modalContainer = document.querySelector(
        ".slds-modal.slds-fade-in-open"
      );

      if (quickActionWrapper || modalContainer) {
        this.isRecordAction = true;
        console.log(
          "Detected Quick Action context - will close outer modal when our modal closes"
        );
      }
    }, 100);
  }

  cleanupModalArtifacts() {
    // Clean up any lingering modal close buttons from previous modal interactions
    setTimeout(() => {
      const lingeringCloseButtons = document.querySelectorAll(
        ".slds-modal__close.closeIcon, .slds-button_icon.slds-modal__close"
      );
      lingeringCloseButtons.forEach((button) => {
        const parentModal = button.closest(".slds-modal");
        if (
          !parentModal ||
          !parentModal.classList.contains("slds-fade-in-open")
        ) {
          console.log(
            "Cleanup: Removing lingering close button on load:",
            button
          );
          button.remove();
        }
      });
    }, 500); // Wait a bit longer on load to ensure other components have rendered
  }

  disconnectedCallback() {
    console.log("disconnectedCallback called - cleaning up");
    // Ensure complete cleanup when component is destroyed
    this.showModal = false;
    this.selectedUsers = [];
    this.searchTerm = "";

    // Final cleanup of any modal artifacts
    this.cleanupModalArtifacts();
  }

  renderedCallback() {
    console.log("renderedCallback called");
    console.log("recordId at renderedCallback:", this.recordId);

    // Only run this once and only if we have a recordId
    if (!this.hasRendered && this.recordId) {
      this.hasRendered = true;
      console.log(
        "Loading followers in renderedCallback with recordId:",
        this.recordId
      );
      this.loadCurrentFollowers();
    }
  }

  @wire(getAvailableUsers)
  wiredUsers(result) {
    this.wiredUsersResult = result;
    if (result.data) {
      this.availableUsers = result.data;
    } else if (result.error) {
      this.showToast("Error", "Failed to load users", "error");
    }
  }

  async loadCurrentFollowers() {
    console.log("loadCurrentFollowers called with recordId:", this.recordId);

    if (!this.recordId) {
      console.warn("Cannot load followers: recordId is null or undefined");
      return;
    }

    try {
      this.currentFollowers = await getCurrentFollowers({
        caseId: this.recordId
      });
      console.log("currentFollowers -> ", this.currentFollowers);
    } catch (error) {
      console.error("Error in loadCurrentFollowers:", error);
      this.showToast("Error", "Failed to load current followers", "error");
    }
  }

  get filteredUsers() {
    if (!this.searchTerm) return this.availableUsers;
    return this.availableUsers.filter(
      (user) =>
        user.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  // followersCount getter removed - no longer needed for inline display

  get hasSelectedUsers() {
    return this.selectedUsers.length > 0;
  }

  get isFollowTab() {
    return this.activeTab === "follow";
  }

  get isManageTab() {
    return this.activeTab === "manage";
  }

  // openModal() method removed - modal shows immediately when component loads

  closeModal() {
    console.log("closeModal called");

    // Clear all state first
    this.showModal = false;
    this.selectedUsers = [];
    this.searchTerm = "";

    // Use setTimeout to ensure DOM cleanup happens after render cycle
    setTimeout(() => {
      // Clean up any lingering popup elements from this component
      const popupContainers = this.template.querySelectorAll(
        ".floating-popup-container"
      );
      popupContainers.forEach((element) => {
        element.style.display = "none";
        element.remove();
      });

      // Also clean up any close buttons that might be lingering
      const closeButtons = this.template.querySelectorAll(".close-button");
      closeButtons.forEach((element) => {
        element.style.display = "none";
        element.remove();
      });

      // Clean up any floating elements in the document that might be from this component
      const allFloatingPopups = document.querySelectorAll(
        '.floating-popup-container[data-component="caseFollowLWC"]'
      );
      allFloatingPopups.forEach((element) => {
        console.log("Removing lingering popup element:", element);
        element.remove();
      });

      // CRITICAL: Clean up any lingering SLDS modal close buttons that might be floating
      // These include the specific button from the Quick Action modal
      const lingeringCloseButtons = document.querySelectorAll(
        '.slds-modal__close.closeIcon, .slds-button_icon.slds-modal__close, button[title="Cancel and close"]'
      );
      lingeringCloseButtons.forEach((button) => {
        // Check if the button is not attached to a visible modal
        const parentModal = button.closest(".slds-modal");
        if (
          !parentModal ||
          parentModal.style.display === "none" ||
          !parentModal.classList.contains("slds-fade-in-open")
        ) {
          console.log("Removing lingering close button:", button);
          button.remove();
        }
      });

      // Also clean up any orphaned modal containers that might be hidden but still in DOM
      const hiddenModals = document.querySelectorAll(
        ".slds-modal:not(.slds-fade-in-open)"
      );
      hiddenModals.forEach((modal) => {
        if (modal.style.display === "none" || modal.offsetParent === null) {
          console.log("Removing hidden modal:", modal);
          modal.remove();
        }
      });

      console.log("DOM cleanup completed");
    }, 100);

    // Always try to close the Quick Action modal since we're clearly in that context
    // (based on the HTML structure you provided)
    try {
      console.log("Attempting to close Quick Action modal...");
      this.dispatchEvent(new CloseActionScreenEvent());
      console.log("CloseActionScreenEvent dispatched successfully");
    } catch (error) {
      console.log(
        "CloseActionScreenEvent failed (might not be in Quick Action context):",
        error
      );

      // Fallback: Try to programmatically close the outer modal
      const outerModal = document.querySelector(
        ".slds-modal.slds-fade-in-open"
      );
      if (outerModal) {
        console.log("Found outer modal, attempting to close it");
        outerModal.classList.remove("slds-fade-in-open");
        outerModal.style.display = "none";

        // Also remove any backdrop
        const backdrop = document.querySelector(".slds-backdrop");
        if (backdrop) {
          backdrop.remove();
        }
      }
    }

    console.log("closeModal completed, showModal:", this.showModal);
  }

  handleTabChange(event) {
    console.log("handleTabChange called, event:", event);
    console.log("event.target:", event.target);
    console.log("event.target.value:", event.target.value);

    // For lightning-tab onactive event, the tab value is in event.target.value
    const newTabValue = event.target.value;

    console.log("Previous activeTab:", this.activeTab);
    console.log("New tab value:", newTabValue);

    this.activeTab = newTabValue;
    this.selectedUsers = [];

    console.log("Updated activeTab:", this.activeTab);
    console.log("isFollowTab:", this.isFollowTab);
    console.log("isManageTab:", this.isManageTab);
  }

  handleSearchChange(event) {
    this.searchTerm = event.target.value;
  }

  handleUserSelection(event) {
    const userId = event.currentTarget.dataset.userId;
    const isSelected = event.currentTarget.classList.contains("selected");

    if (isSelected) {
      this.selectedUsers = this.selectedUsers.filter((id) => id !== userId);
      event.currentTarget.classList.remove("selected");
    } else {
      this.selectedUsers = [...this.selectedUsers, userId];
      event.currentTarget.classList.add("selected");
    }
  }

  async handleFollow() {
    if (this.selectedUsers.length === 0) {
      this.showToast("Warning", "Please select at least one user", "warning");
      return;
    }

    this.isLoading = true;
    try {
      const result = await followCase({
        caseId: this.recordId,
        userIds: this.selectedUsers
      });

      this.showToast("Success", result, "success");
      this.closeModal();
      await this.loadCurrentFollowers();
    } catch (error) {
      this.showToast("Error", error.body.message, "error");
    } finally {
      this.isLoading = false;
    }
  }

  async handleUnfollow() {
    if (this.selectedUsers.length === 0) {
      this.showToast(
        "Warning",
        "Please select at least one user to unfollow",
        "warning"
      );
      return;
    }

    this.isLoading = true;
    try {
      const result = await unfollowCase({
        caseId: this.recordId,
        userIds: this.selectedUsers
      });

      this.showToast("Success", result, "success");
      this.closeModal();
      await this.loadCurrentFollowers();
    } catch (error) {
      this.showToast("Error", error.body.message, "error");
    } finally {
      this.isLoading = false;
    }
  }

  showToast(title, message, variant) {
    this.dispatchEvent(
      new ShowToastEvent({
        title,
        message,
        variant
      })
    );
  }
}