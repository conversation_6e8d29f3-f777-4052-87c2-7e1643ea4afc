/********************************************************************************************

   @ Func Area	:  Opportunity Management

   @ Author	:  <PERSON>

   @ Date	:  28 January 2025

   @ Description	:   This class is responsible for processing Opportunity Line Items after they are committed to the Database.
                        Specifically, it reacts to changes in custom financial fields and updates the standard UnitPrice field.
                        It operates in the After Insert, After Update, and After Delete contexts.

   @ Developer Notes   :   The primary logic resides in the afterUpdate method and its subsequent asynchronous call.

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                        https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 28 January 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/
public without sharing class TA_OppLineItem_FinanceCalcs implements TriggerAction.AfterInsert, TriggerAction.AfterUpdate, TriggerAction.AfterDelete {
  //Dependent Class variables
  @TestVisible
  private static OpportunityLineItemScheduleSelector opportunityLineItemScheduleSelector = new OpportunityLineItemScheduleSelector(); //Mock OpportunityLineItemScheduleSelector Class
  @TestVisible
  private static DmlHelper dmlHelper = new DmlHelper(); //Mock DmlHelper Class
  @TestVisible
  private static OpportunityService opportunityService = new OpportunityService(); //Mock OpportunityService Class
  @TestVisible
  private static OpportunityLineItemService opportunityLineItemService = new OpportunityLineItemService(); //Mock OpportunityLineItemService Class
  @TestVisible
  private static OpportunityLineItemSelector opportunityLineItemSelector = new OpportunityLineItemSelector(); //Mock OpportunityLineItemSelector Class
  @TestVisible
  private static OpportunitySelector opportunitySelector = new OpportunitySelector(); //Mock OpportunitySelector Class

  //Class variables
  @TestVisible
  private static Boolean BYPASS_VALIDATION_RULE = false;
  List<OpportunityLineItem> opportunityLineItemsToProcess = new List<OpportunityLineItem>();
  List<OpportunityLineItem> opportunityLineItemsToUpdate = new List<OpportunityLineItem>();
  Set<Id> opportunityIdsToProcess = new Set<Id>();

  /**
   * @description Executes after OpportunityLineItem records are inserted.
   *              Required by TriggerAction.AfterInsert interface. Currently no specific logic implemented here.
   * @param newList The list of newly inserted OpportunityLineItem records.
   */
  public void afterInsert(List<OpportunityLineItem> newList) {
    // Currently no action needed after insert for this specific logic.
    for (OpportunityLineItem newRecord : newList) {
    }
  }

  /**
   * @description Executes after OpportunityLineItem records are updated.
   *              Checks if specific financial fields have changed and triggers an asynchronous update
   *              to recalculate and set the UnitPrice.
   * @param newList The list of updated OpportunityLineItem records (after update).
   * @param oldList The list of OpportunityLineItem records before the update.
   */
  public void afterUpdate(List<OpportunityLineItem> newList, List<OpportunityLineItem> oldList) {
    Map<Id, OpportunityLineItem> oldMap = new Map<Id, OpportunityLineItem>(oldList);
    // Check if any monitored financial fields changed during the update.
    for (OpportunityLineItem newRecord : newList) {
      OpportunityLineItem oldRecord = oldMap.get(newRecord.Id);
      // Use the helper method to compare old and new values of specified fields.
      if (TA_OppLineItem_FinanceCalcs.haveFieldsChanged(oldRecord, newRecord)) {
        opportunityLineItemsToProcess.add(newRecord);
      }
    }

    // Only proceed if there are records that actually require financial calculation updates.
    if (CollectionUtils.isEmpty(opportunityLineItemsToProcess)) {
      return; // Exit if no relevant changes were detected.
    }

    // Initiate the financial calculation and update process.
    calculateOpportunityLineItemFinancials(opportunityLineItemsToProcess);
  }

  /**
   * @description Executes after OpportunityLineItem records are deleted.
   *              Required by TriggerAction.AfterDelete interface. Currently no specific logic implemented here.
   * @param oldList The list of OpportunityLineItem records that were deleted.
   */
  public void afterDelete(List<OpportunityLineItem> oldList) {
    // Currently no action needed after delete for this specific logic.
  }

  /************************************************** Private Methods *****************************************************************/

  /**
   * @description Compares specific financial fields between the old and new versions of an OpportunityLineItem record.
   * @param oldRecord The OpportunityLineItem record before the update.
   * @param newRecord The OpportunityLineItem record after the update.
   * @return Boolean True if any of the monitored fields have changed, false otherwise.
   */
  private static Boolean haveFieldsChanged(
    OpportunityLineItem oldRecord,
    OpportunityLineItem newRecord
  ) {
    // Defines the set of custom financial fields to monitor for changes.
    Set<String> fieldsToCheck = new Set<String>{
      'CYUnweightedRevenue__c',
      'CYWeightedRevenue__c',
      'NextCYUnweightedRevenue__c',
      'NextCYWeightedRevenue__c',
      'CalendarYear__c',
      'NextCalendarYear__c'
    };

    // Iterate through the monitored fields and compare old vs. new values.
    for (String field : fieldsToCheck) {
      if (oldRecord.get(field) != newRecord.get(field)) {
        return true; // Return true immediately if a change is found.
      }
    }
    return false; // Return false if no changes were detected in the monitored fields.
  }

  /**
   * @description Prepares the list of OpportunityLineItem IDs that need financial updates and calls the asynchronous update method.
   * @param opportunityLineItemsToProcess The list of OpportunityLineItem records identified as having relevant field changes.
   */
  private void calculateOpportunityLineItemFinancials(
    List<OpportunityLineItem> opportunityLineItemsToProcess
  ) {
    // Collect the IDs of the OpportunityLineItems that need processing.
    Set<Id> opportunityLineItemIds = new Set<Id>();
    for (OpportunityLineItem oli : opportunityLineItemsToProcess) {
      opportunityLineItemIds.add(oli.Id);
    }

    // Call the asynchronous (@future) method to perform the actual update.
    // This is done asynchronously to prevent mixed DML errors and potential recursion,
    // as we are updating the same records that triggered this logic.
    updateRecordsAsync(opportunityLineItemIds);
  }

  /**
   * @description Asynchronous method (@future) to update the UnitPrice on OpportunityLineItems.
   *              This runs in a separate transaction after the initial trigger context completes.
   * @param opportunityLineItemIds A set of IDs for the OpportunityLineItem records to update.
   */
  @future
  public static void updateRecordsAsync(Set<Id> opportunityLineItemIds) {
    List<OpportunityLineItem> opportunityLineItemsToUpdateAsync = new List<OpportunityLineItem>();
    // Re-query the OpportunityLineItem records within the future context to ensure fresh data.
    // Uses the selector pattern for querying.
    List<OpportunityLineItem> opportunityLineItemsToEvaluate = opportunityLineItemSelector.selectOpportunityLineItemsByIds(
      opportunityLineItemIds
    );

    // Process the queried records to prepare updates.
    if (CollectionUtils.isNotEmpty(opportunityLineItemsToEvaluate)) {
      for (OpportunityLineItem oli : opportunityLineItemsToEvaluate) {
        // Set the standard UnitPrice (API name: UnitPrice) based on the custom CYUnweightedRevenue__c field.
        Decimal newSalesPrice = oli.CYUnweightedRevenue__c;

        if (newSalesPrice != null) {
          // Add a new OpportunityLineItem instance (with only Id and the field to update) to the update list.
          opportunityLineItemsToUpdateAsync.add(
            new OpportunityLineItem(Id = oli.Id, UnitPrice = newSalesPrice)
          );
        }
      }
    }

    // Perform the DML update operation only if there are records to update.
    // Uses the service layer pattern for DML.
    if (CollectionUtils.isNotEmpty(opportunityLineItemsToUpdateAsync)) {
      opportunityLineItemService.updateOpportunityLineItems(
        opportunityLineItemsToUpdateAsync,
        'TA_OppLineItem_FinanceCalcs.afterUpdate @Future context' // Context message for logging/debugging in the service layer.
      );
    }
  }
}
